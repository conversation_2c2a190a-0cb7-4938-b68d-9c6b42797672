#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试销售专家修复效果
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from module2 import _is_valid_analysis_result

def test_analysis_result_validation():
    """测试分析结果验证功能"""
    print("🧪 测试分析结果验证功能")
    print("=" * 50)
    
    # 测试用例1: 有效的分析结果
    valid_result = """
**Website Recommendations:**
1. **Industry News**: https://www.chemicalprocessing.com
2. **Trade Association**: https://www.aiche.org
3. **Business Directory**: https://www.thomasnet.com
4. **Government Platform**: https://www.sam.gov
5. **Industry Forum**: https://www.chemicalforums.com

**Keywords:**
[chemical synthesis equipment, pharmaceutical manufacturing, biotech equipment, process automation, chemical reactors]
"""
    
    print("\n📋 测试用例1: 有效的分析结果")
    result1 = _is_valid_analysis_result(valid_result)
    print(f"✅ 验证结果: {result1} (期望: True)")
    
    # 测试用例2: 无效的分析结果（提示词重复）
    invalid_result = """
You are an expert in recommending industry websites.

**Task Steps:**
1. Based on the provided product information, analyze the application fields...
"""
    
    print("\n📋 测试用例2: 无效的分析结果（提示词重复）")
    result2 = _is_valid_analysis_result(invalid_result)
    print(f"✅ 验证结果: {result2} (期望: False)")
    
    # 测试用例3: 内容过短
    short_result = "短内容"
    
    print("\n📋 测试用例3: 内容过短")
    result3 = _is_valid_analysis_result(short_result)
    print(f"✅ 验证结果: {result3} (期望: False)")
    
    # 测试用例4: 缺少关键元素
    incomplete_result = """
这是一些分析内容，但是缺少网站推荐和关键词。
只有一些行业信息和市场分析。
"""
    
    print("\n📋 测试用例4: 缺少关键元素")
    result4 = _is_valid_analysis_result(incomplete_result)
    print(f"✅ 验证结果: {result4} (期望: False)")
    
    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    print(f"   - 有效结果识别: {'✅' if result1 else '❌'}")
    print(f"   - 提示词重复检测: {'✅' if not result2 else '❌'}")
    print(f"   - 短内容检测: {'✅' if not result3 else '❌'}")
    print(f"   - 不完整内容检测: {'✅' if not result4 else '❌'}")
    
    all_passed = result1 and not result2 and not result3 and not result4
    print(f"\n🏆 总体测试结果: {'✅ 全部通过' if all_passed else '❌ 存在问题'}")
    
    return all_passed

if __name__ == "__main__":
    test_analysis_result_validation()
