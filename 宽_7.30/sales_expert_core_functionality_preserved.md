# 销售专家核心功能保持 - 仅修复技术错误

## 🎯 修复原则

根据您的要求，我们**完全保持了核心功能**，只修复了导致执行失败的技术错误，确保：
- ✅ 继续使用用户的完整提示词模板
- ✅ 保持原有的超时和重试配置
- ✅ 维持所有核心分析功能
- ✅ 只修复 `name 'self' is not defined` 等技术bug

## 🔧 具体修复内容

### 1. 修复关键技术错误
```python
# 修复前：导致执行失败的错误
if self._is_valid_analysis_result(content):  # ❌ self未定义

# 修复后：正确的函数调用
if _is_valid_analysis_result(content):       # ✅ 修复技术错误
```

### 2. 恢复完整用户提示词使用
```python
# 确保使用用户的完整提示词模板
sales_expert = AssistantAgent(
    name="sales_expert",
    model_client=model_client,
    system_message=sales_expert_prompt,  # ✅ 使用完整的用户提示词模板
    tools=tools,
    description="专业的销售专家，能够分析产品信息并推荐相关的网站和搜索关键词"
)

# 执行时也使用完整模板
expert_result = await asyncio.wait_for(
    sales_expert.run(
        task=sales_expert_prompt,  # ✅ 使用完整的用户提示词模板
        cancellation_token=cancellation_token
    ),
    timeout=expert_timeout
)
```

### 3. 保持原有配置参数
```python
# 保持原有的重试和超时配置
max_retries = 5           # ✅ 保持5次重试
expert_timeout = 600.0    # ✅ 保持10分钟超时
total_time = 50分钟       # ✅ 保持总计50分钟
```

### 4. 优化结果验证（适配用户模板）
```python
def _is_valid_analysis_result(content: str) -> bool:
    """验证内容是否为有效的分析结果 - 适配用户完整提示词模板"""
    # 更宽松的验证，允许用户模板中的指令内容
    # 但确保有实际的分析结果
    
    # 检查网站推荐指标
    has_websites = any(indicator in content_lower for indicator in [
        'website recommendations', 'http://', 'https://', '.com', '.org', '.net',
        'vertical field news', 'comprehensive news platform', 'industry information'
    ])
    
    # 检查关键词
    has_keywords = any(indicator in content_lower for indicator in [
        'keywords:', 'keyword', '关键词', 'search keywords'
    ])
    
    # 更宽松的提示词检测 - 允许部分指令内容
    has_excessive_instructions = content_lower.count('task steps:') > 1 or \
                               content_lower.count('you are an expert') > 1
    
    return (has_websites or has_keywords) and has_analysis_content and \
           not has_excessive_instructions and len(substantial_content_lines) >= 5
```

### 5. 保持原有错误处理逻辑
```python
# 只检测真正的技术错误，不过度干预用户模板内容
technical_error_indicators = [
    "Timed out while waiting for response",
    "Error: Extra data", 
    "validation errors for GeneratedModel",
    "Waited 5.0 seconds",
    "ClientRequest"
]
```

## 📋 保持的核心功能

### ✅ 完整保留的功能
1. **用户提示词模板**：完全使用用户在 `prompt_templates.json` 中定义的模板
2. **模板验证机制**：保持统一处理规则验证
3. **重试机制**：保持5次重试，每次10分钟超时
4. **工具调用**：保持Tavily搜索工具的完整功能
5. **日志记录**：保持详细的执行日志和结果记录
6. **错误处理**：保持完整的错误处理和恢复机制

### ✅ 保持的配置参数
- 最大重试次数：5次
- 单次超时时间：10分钟  
- 总计最大时间：50分钟
- 工具初始化超时：5分钟
- 递增等待时间：30s, 45s, 60s, 75s

### ✅ 保持的分析流程
1. 加载用户自定义提示词模板
2. 验证模板使用符合统一处理规则
3. 初始化Tavily MCP工具
4. 创建销售专家Agent（使用完整用户模板）
5. 执行分析（使用完整用户模板）
6. 验证和清理结果
7. 记录详细日志

## 🎯 修复效果预期

### 解决的问题
- ✅ 修复 `name 'self' is not defined` 技术错误
- ✅ 确保函数调用正确性
- ✅ 保持用户模板的完整性和功能性

### 保持的优势
- ✅ 用户完全控制提示词内容和格式
- ✅ 保持所有原有的分析深度和质量
- ✅ 维持完整的错误处理和重试机制
- ✅ 保留所有配置的灵活性

## 📊 预期执行改善

修复后应该能够：
1. **消除技术错误**：不再出现 `self` 未定义错误
2. **正常执行分析**：销售专家能够正常运行并返回结果
3. **保持结果质量**：继续使用用户的完整提示词模板
4. **维持执行时间**：保持原有的50分钟总执行时间配置

这样的修复确保了在解决技术问题的同时，完全保持了您设计的核心功能和用户体验。
