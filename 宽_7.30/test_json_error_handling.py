#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON错误处理功能
"""
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from module2 import handle_json_decode_error

def test_json_error_handling():
    """测试JSON错误处理功能"""
    print("🧪 测试JSON错误处理功能")
    print("=" * 50)
    
    # 测试用例1: 空响应
    print("\n📋 测试用例1: 空响应")
    try:
        json.loads("")
    except json.JSONDecodeError as e:
        error_msg = handle_json_decode_error(e, "空响应测试")
        print(f"✅ 捕获到错误: {error_msg}")
    
    # 测试用例2: 截断的JSON
    print("\n📋 测试用例2: 截断的JSON")
    try:
        truncated_json = '{"name": "test", "data": [1, 2, 3'  # 缺少结束括号
        json.loads(truncated_json)
    except json.JSONDecodeError as e:
        error_msg = handle_json_decode_error(e, "截断JSON测试")
        print(f"✅ 捕获到错误: {error_msg}")
    
    # 测试用例3: 多个JSON对象
    print("\n📋 测试用例3: 多个JSON对象")
    try:
        multiple_json = '{"first": "object"}{"second": "object"}'
        json.loads(multiple_json)
    except json.JSONDecodeError as e:
        error_msg = handle_json_decode_error(e, "多JSON对象测试")
        print(f"✅ 捕获到错误: {error_msg}")
    
    # 测试用例4: 长响应中的错误
    print("\n📋 测试用例4: 长响应中的错误")
    try:
        # 创建一个长的JSON字符串，在中间有错误
        long_json = '{"data": "' + "x" * 1500 + '", "error": invalid_value}'
        json.loads(long_json)
    except json.JSONDecodeError as e:
        error_msg = handle_json_decode_error(e, "长响应测试")
        print(f"✅ 捕获到错误: {error_msg}")
    
    print("\n✅ JSON错误处理测试完成")

def simulate_api_response_error():
    """模拟API响应错误的情况"""
    print("\n🔄 模拟API响应错误情况")
    print("=" * 50)
    
    # 模拟常见的API响应错误
    error_scenarios = [
        ("空响应", ""),
        ("HTML错误页面", "<html><body>Error 500</body></html>"),
        ("部分JSON", '{"status": "ok", "data":'),
        ("无效字符", '{"message": "test\x00invalid"}'),
        ("编码问题", b'\xff\xfe{"test": "value"}'),
    ]
    
    for scenario_name, response_data in error_scenarios:
        print(f"\n📋 场景: {scenario_name}")
        try:
            if isinstance(response_data, bytes):
                # 尝试解码字节数据
                try:
                    response_data = response_data.decode('utf-8')
                except UnicodeDecodeError:
                    response_data = response_data.decode('utf-8', errors='ignore')
            
            json.loads(response_data)
            print("⚠️ 意外成功解析")
        except json.JSONDecodeError as e:
            error_msg = handle_json_decode_error(e, f"{scenario_name}场景")
            print(f"✅ 正确处理错误: {error_msg}")
        except Exception as e:
            print(f"⚠️ 其他错误: {type(e).__name__}: {e}")

def test_error_recovery_suggestions():
    """测试错误恢复建议"""
    print("\n💡 测试错误恢复建议")
    print("=" * 50)
    
    # 测试不同类型的错误是否给出合适的建议
    test_cases = [
        ('', "空响应"),
        ('{"incomplete":', "不完整JSON"),
        ('{"valid": "json"}{"extra": "data"}', "额外数据"),
        ('x' * 2000 + '{"data": "test"}', "长响应"),
    ]
    
    for test_data, description in test_cases:
        print(f"\n📋 测试: {description}")
        try:
            json.loads(test_data)
        except json.JSONDecodeError as e:
            error_msg = handle_json_decode_error(e, description)
            print(f"建议: {error_msg}")

if __name__ == "__main__":
    print("🚀 启动JSON错误处理测试")
    
    try:
        test_json_error_handling()
        simulate_api_response_error()
        test_error_recovery_suggestions()
        
        print("\n🎉 所有测试完成！")
        print("💡 JSON错误处理功能已准备就绪")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
