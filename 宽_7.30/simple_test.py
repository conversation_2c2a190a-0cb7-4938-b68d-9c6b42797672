#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的实时日志功能测试
"""
import gradio as gr
import threading
import time
from datetime import datetime

# 全局变量
_current_log = ""
_lock = threading.Lock()
_running = False

def update_log(message):
    """更新日志"""
    global _current_log, _lock
    with _lock:
        timestamp = datetime.now().strftime('%H:%M:%S')
        _current_log += f"[{timestamp}] {message}\n"

def get_log():
    """获取日志"""
    global _current_log, _lock
    with _lock:
        return _current_log

def clear_log():
    """清空日志"""
    global _current_log, _lock
    with _lock:
        _current_log = ""

def simulate_work():
    """模拟工作过程"""
    global _running
    _running = True
    clear_log()
    
    steps = [
        "开始初始化...",
        "加载配置文件...",
        "连接到服务器...",
        "开始数据处理...",
        "处理第1批数据...",
        "处理第2批数据...",
        "处理第3批数据...",
        "生成报告...",
        "保存结果...",
        "任务完成！"
    ]
    
    for step in steps:
        update_log(step)
        time.sleep(2)  # 每步等待2秒
    
    _running = False
    return "✅ 任务完成", get_log()

def create_interface():
    """创建界面"""
    with gr.Blocks(title="实时日志测试") as demo:
        gr.HTML("<h1>🤖 实时日志测试</h1>")
        
        with gr.Row():
            start_btn = gr.Button("🚀 开始任务", variant="primary")
            clear_btn = gr.Button("🧹 清空日志", variant="secondary")
        
        with gr.Row():
            log_display = gr.Textbox(
                label="实时日志",
                lines=10,
                interactive=False,
                placeholder="点击开始任务后，日志将在这里显示..."
            )
        
        with gr.Row():
            result_display = gr.Textbox(
                label="结果",
                lines=3,
                interactive=False
            )
        
        # 定时器每2秒更新一次日志
        timer = gr.Timer(value=2.0)
        timer.tick(fn=get_log, outputs=[log_display])
        
        # 绑定按钮事件
        start_btn.click(
            fn=simulate_work,
            outputs=[result_display, log_display]
        )
        
        clear_btn.click(
            fn=lambda: (clear_log() or "", ""),
            outputs=[log_display, result_display]
        )
    
    return demo

if __name__ == "__main__":
    print("🚀 启动简单测试程序...")
    demo = create_interface()
    demo.launch(server_port=7862, share=False)
