Project 3:
News / Article Title:
Project Name:
company:
Project Description:
Why is this an opportunity：
Project Time:
Project Location:
Project Website: 

Project......
"
--------------------------------------------------------------------------------
📊 提示词长度: 6510 字符
================================================================================

🎯 开始智能深度分析 - 使用纯用户提示词模式
📋 MagenticOne直接使用用户提示词，无系统提示词干扰
✅ 用户完全控制分析流程和输出格式
⏱️ 设置分析超时时间: 25.0分钟 (基于5个商机)
🤖 使用智能分析引擎执行深度分析...
❌ 智能分析出错: BaseGroupChat.run_stream() got an unexpected keyword argument 'termination_condition'

=== 执行时间统计 ===
📊 第一阶段（销售专家分析）: 16.7秒
🚀 第二阶段（智能深度挖掘）: 0.0秒
⏱️ 总耗时: 20.3秒

详细时间分布：
- 第一阶段占比: 82.0%
- 第二阶段占比: 0.0%

错误详情: 执行过程中出现错误: 智能分析失败: BaseGroupChat.run_stream() got an unexpected keyword argument 'termination_condition'
Traceback (most recent call last):
  File "D:\kuan\宽_7.30\module2.py", line 2522, in analyze_business_opportunities
    Console(m1_agent.run_stream(task=m1_task, termination_condition=termination_condition)),
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: BaseGroupChat.run_stream() got an unexpected keyword argument 'termination_condition'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\kuan\宽_7.30\module2.py", line 2557, in analyze_business_opportunities
    raise RuntimeError(f"智能分析失败: {error_str}") from e
RuntimeError: 智能分析失败: BaseGroupChat.run_stream() got an unexpected keyword argument 'termination_condition'


=== 分析完成 - 2025-07-31 00:25:54 ===

=== 执行时间统计 ===
📊 第一阶段（销售专家分析）: 16.7秒
🚀 第二阶段（智能深度挖掘）: 0.0秒
⏱️ 总耗时: 20.3秒

详细时间分布：
- 第一阶段占比: 82.0%
- 第二阶段占比: 0.0%