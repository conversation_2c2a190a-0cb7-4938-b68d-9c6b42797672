#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MagenticOne ledger错误处理
"""
import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ledger_error_detection():
    """测试ledger错误检测逻辑"""
    print("🧪 测试ledger错误检测逻辑")
    print("=" * 50)
    
    # 模拟不同类型的错误
    test_errors = [
        ("ValueError: Failed to parse ledger information after multiple retries.", "ledger错误"),
        ("ValueError: 提取的分析结果内容不足", "结果提取错误"),
        ("ValueError: 其他数据处理问题", "一般ValueError"),
        ("JSONDecodeError: Expecting value", "JSON错误"),
        ("RuntimeError: 网络连接问题", "一般错误"),
        ("Exception: magentic one orchestrator error", "MagenticOne相关错误"),
    ]
    
    print("📋 错误检测测试结果:")
    for error_msg, error_type in test_errors:
        # 检测ledger错误
        is_ledger_error = "Failed to parse ledger information" in error_msg
        is_result_error = "提取的分析结果内容不足" in error_msg
        is_json_error = "JSONDecodeError" in error_msg or "Expecting value" in error_msg
        is_magentic_error = any(keyword in error_msg.lower() for keyword in ["magentic", "orchestrator", "ledger"])
        
        print(f"\n📋 {error_type}:")
        print(f"   错误信息: {error_msg}")
        print(f"   Ledger错误: {'✅' if is_ledger_error else '❌'}")
        print(f"   结果错误: {'✅' if is_result_error else '❌'}")
        print(f"   JSON错误: {'✅' if is_json_error else '❌'}")
        print(f"   MagenticOne错误: {'✅' if is_magentic_error else '❌'}")
    
    print("\n✅ 错误检测逻辑测试完成")

def test_retry_mechanism():
    """测试重试机制逻辑"""
    print("\n🧪 测试重试机制逻辑")
    print("=" * 50)
    
    async def simulate_retry_logic():
        max_retries = 3
        retry_count = 0
        result = None
        
        print("📋 模拟重试流程:")
        
        while retry_count < max_retries:
            try:
                retry_count += 1
                print(f"   尝试第{retry_count}次执行...")
                
                # 模拟ledger错误（前2次失败，第3次成功）
                if retry_count < 3:
                    raise ValueError("Failed to parse ledger information after multiple retries.")
                else:
                    result = "分析成功完成"
                    print(f"   ✅ 第{retry_count}次执行成功")
                    break
                    
            except ValueError as ve:
                if "Failed to parse ledger information" in str(ve):
                    print(f"   ⚠️ 检测到ledger错误 (第{retry_count}/{max_retries}次)")
                    if retry_count < max_retries:
                        wait_time = 30 + (retry_count * 15)
                        print(f"   🔄 等待{wait_time}秒后重试...")
                        # 模拟等待（实际中是 await asyncio.sleep(wait_time)）
                        print(f"   🔄 重新初始化分析引擎...")
                        continue
                    else:
                        print(f"   ❌ 重试{max_retries}次后仍然失败")
                        raise
                else:
                    raise
        
        if result:
            print(f"✅ 重试机制成功，最终结果: {result}")
        else:
            print("❌ 重试机制失败")
        
        return result
    
    # 运行异步测试
    try:
        result = asyncio.run(simulate_retry_logic())
        print(f"✅ 重试机制测试通过: {result}")
        return True
    except Exception as e:
        print(f"❌ 重试机制测试失败: {e}")
        return False

def test_error_message_generation():
    """测试错误信息生成"""
    print("\n🧪 测试错误信息生成")
    print("=" * 50)
    
    error_scenarios = [
        ("Failed to parse ledger information after multiple retries.", 
         "智能分析引擎遇到内部处理问题，建议减少商机数量或稍后重试。原因：分析引擎内部状态管理异常。"),
        ("提取的分析结果内容不足", 
         "智能分析未能生成完整结果，建议减少商机数量或检查网络连接后重试。"),
        ("其他数据处理问题", 
         "智能分析过程中遇到数据处理问题: 其他数据处理问题。建议检查输入参数或稍后重试。"),
    ]
    
    print("📋 错误信息生成测试:")
    for original_error, expected_message in error_scenarios:
        print(f"\n📋 原始错误: {original_error}")
        print(f"   期望信息: {expected_message}")
        
        # 模拟错误处理逻辑
        if "Failed to parse ledger information" in original_error:
            generated_message = "智能分析引擎遇到内部处理问题，建议减少商机数量或稍后重试。原因：分析引擎内部状态管理异常。"
        elif "提取的分析结果内容不足" in original_error:
            generated_message = "智能分析未能生成完整结果，建议减少商机数量或检查网络连接后重试。"
        else:
            generated_message = f"智能分析过程中遇到数据处理问题: {original_error}。建议检查输入参数或稍后重试。"
        
        match = generated_message == expected_message
        print(f"   生成信息: {generated_message}")
        print(f"   匹配结果: {'✅' if match else '❌'}")
    
    print("\n✅ 错误信息生成测试完成")

def verify_code_implementation():
    """验证代码实现"""
    print("\n🧪 验证代码实现")
    print("=" * 50)
    
    try:
        with open('module2.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键实现
        checks = [
            ("ValueError专门处理", "except ValueError as e:"),
            ("ledger错误检测", "Failed to parse ledger information"),
            ("重试机制", "max_retries = 3"),
            ("重试循环", "while retry_count < max_retries:"),
            ("重新初始化", "m1_agent = MagenticOne(client=model_client"),
            ("等待重试", "await asyncio.sleep(wait_time)"),
            ("MagenticOne错误检测", "magentic.*orchestrator.*ledger"),
            ("用户友好错误", "建议减少商机数量或稍后重试"),
        ]
        
        print("📋 代码实现检查:")
        all_found = True
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"✅ {check_name}: 已实现")
            else:
                print(f"❌ {check_name}: 未找到")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ 验证代码实现时出错: {e}")
        return False

def show_implementation_summary():
    """显示实现总结"""
    print("\n💡 MagenticOne ledger错误处理实现总结")
    print("=" * 50)
    
    features = [
        "🎯 专门的ValueError处理: 区分ledger错误和其他错误",
        "🔄 智能重试机制: 最多重试3次，递增等待时间",
        "🔧 自动重新初始化: 重试时重新创建MagenticOne实例",
        "⏱️ 合理等待时间: 30s, 45s, 60s递增等待",
        "🛡️ 错误分类处理: 不同错误类型不同处理策略",
        "📝 用户友好提示: 将技术错误转换为可理解的建议",
        "🔍 精确错误检测: 准确识别ledger相关错误",
        "💾 状态保持: 保持max_turns等配置参数"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n📊 重试策略:")
    print("   - 第1次失败: 等待30秒后重试")
    print("   - 第2次失败: 等待45秒后重试")
    print("   - 第3次失败: 等待60秒后重试")
    print("   - 3次后仍失败: 抛出用户友好的错误信息")
    
    print("\n🎯 预期效果:")
    print("   - 临时性ledger错误: 通过重试自动恢复")
    print("   - 持续性问题: 提供明确的解决建议")
    print("   - 用户体验: 隐藏技术细节，提供可操作的建议")

def main():
    """主测试函数"""
    print("🚀 开始MagenticOne ledger错误处理测试")
    print("=" * 60)
    
    tests = [
        ("ledger错误检测逻辑", test_ledger_error_detection),
        ("重试机制逻辑", test_retry_mechanism),
        ("错误信息生成", test_error_message_generation),
        ("代码实现验证", verify_code_implementation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    # 显示实现总结
    show_implementation_summary()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{status}: {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！")
        print("💡 MagenticOne ledger错误处理已完善")
        print("\n📋 实现特点:")
        print("   🔄 智能重试: 自动处理临时性ledger错误")
        print("   🛡️ 错误分类: 精确识别不同类型的错误")
        print("   📝 用户友好: 提供可理解的错误信息和建议")
        print("   🔧 自动恢复: 重新初始化分析引擎")
        print("\n🚀 现在可以更好地处理MagenticOne内部错误！")
    else:
        print("❌ 部分测试失败，请检查实现")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
