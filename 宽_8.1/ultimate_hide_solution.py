#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极解决方案：彻底隐藏Gradio底部信息栏
"""
import os
import sys

def apply_ultimate_hide_fix():
    """应用终极隐藏修复到module2.py"""
    
    # 读取原文件
    with open('module2.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 确保 gr.Blocks 有 show_api=False
    if 'show_api=False' not in content:
        # 在 gr.Blocks 中添加 show_api=False
        content = content.replace(
            'with gr.Blocks(\n            title="智能商机分析系统",\n            theme=gr.themes.Soft(),',
            'with gr.Blocks(\n            title="智能商机分析系统",\n            theme=gr.themes.Soft(),\n            show_api=False,'
        )
        print("✅ 已在 gr.Blocks 中添加 show_api=False")
    
    # 2. 在所有 interface.launch 中添加 show_api=False
    # 第一个 launch
    content = content.replace(
        'quiet=False              # 显示访问URL信息\n        )',
        'quiet=False,             # 显示访问URL信息\n            show_api=False           # 隐藏底部API信息栏\n        )'
    )
    
    # 第二个 launch
    content = content.replace(
        'quiet=False              # 显示访问URL信息\n            )',
        'quiet=False,             # 显示访问URL信息\n                show_api=False           # 隐藏底部API信息栏\n            )'
    )
    
    print("✅ 已在所有 interface.launch 中添加 show_api=False")
    
    # 3. 添加终极CSS隐藏规则
    ultimate_css = '''
        
        /* ========== 终极底部信息栏隐藏规则 ========== */
        /* 隐藏所有可能的footer元素 */
        .footer, footer, 
        .gradio-container .footer, 
        .gradio-container footer,
        .app .footer, 
        .app footer,
        div[class*="footer"],
        div[class*="Footer"],
        .gradio-container > div:last-child,
        .gradio-container .wrap:last-child,
        .gradio-container .prose:last-child,
        .gradio-container > .wrap > div:last-child,
        body > gradio-app > div:last-child,
        gradio-app > div:last-child,
        #root > div:last-child,
        .gradio-container .svelte-1hnfib2,
        .gradio-container .prose.svelte-1ybaih5,
        .gradio-container .prose,
        .gradio-container div[class*="prose"],
        .gradio-container div[class*="svelte"] {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            overflow: hidden !important;
            opacity: 0 !important;
            position: absolute !important;
            left: -99999px !important;
            top: -99999px !important;
            z-index: -9999 !important;
            pointer-events: none !important;
        }
        
        /* 隐藏包含API和Gradio链接的元素 */
        a[href*="api"], 
        a[href*="gradio"],
        a[href*="/docs"],
        a[href*="localhost"],
        a[href*="127.0.0.1"],
        div:has(a[href*="api"]),
        div:has(a[href*="gradio"]),
        div:has(a[href*="/docs"]) {
            display: none !important;
            visibility: hidden !important;
        }
        
        /* 通过内容匹配隐藏 */
        *:contains("通过 API 使用"),
        *:contains("使用 Gradio 构建"),
        *:contains("设置"),
        *:contains("Built with Gradio"),
        *:contains("API"),
        *:contains("Gradio") {
            display: none !important;
        }
        
        /* 隐藏最后的子元素（通常是footer） */
        body > *:last-child,
        #root > *:last-child,
        .gradio-container > *:last-child {
            display: none !important;
        }'''
    
    # 在CSS末尾添加终极隐藏规则
    if '/* ========== 终极底部信息栏隐藏规则 ========== */' not in content:
        content = content.replace(
            '        }\n        """\n    ) as interface:',
            f'        }}{ultimate_css}\n        """\n    ) as interface:'
        )
        print("✅ 已添加终极CSS隐藏规则")
    
    # 4. 添加终极JavaScript隐藏代码
    ultimate_js = '''
        # ========== 终极JavaScript隐藏代码 ==========
        gr.HTML("""
        <style>
        /* 内联强制隐藏样式 */
        .footer, footer, div[class*="footer"], div[class*="Footer"] { 
            display: none !important; 
            visibility: hidden !important; 
            height: 0 !important;
            position: absolute !important;
            left: -99999px !important;
        }
        .gradio-container > div:last-child,
        .gradio-container .wrap:last-child,
        .gradio-container .prose:last-child { 
            display: none !important; 
        }
        a[href*="api"], a[href*="gradio"], a[href*="/docs"] { 
            display: none !important; 
        }
        </style>
        <script>
        (function() {
            'use strict';
            
            function nuclearHideFooter() {
                // 1. 通过文本内容暴力隐藏
                const textTargets = [
                    '通过 api 使用', '使用 gradio 构建', '设置',
                    'built with gradio', 'api', 'gradio', 'docs'
                ];
                
                document.querySelectorAll('*').forEach(el => {
                    if (el.textContent) {
                        const text = el.textContent.toLowerCase().trim();
                        for (const target of textTargets) {
                            if (text.includes(target) && text.length < 100) {
                                el.style.cssText = 'display: none !important; visibility: hidden !important; height: 0 !important; position: absolute !important; left: -99999px !important;';
                                if (el.parentElement) {
                                    el.parentElement.style.cssText = 'display: none !important;';
                                }
                                if (el.parentElement && el.parentElement.parentElement) {
                                    el.parentElement.parentElement.style.cssText = 'display: none !important;';
                                }
                                break;
                            }
                        }
                    }
                });
                
                // 2. 通过选择器暴力隐藏
                const selectors = [
                    '.footer', 'footer', 
                    '.gradio-container .footer',
                    '.gradio-container footer',
                    'div[class*="footer"]',
                    'div[class*="Footer"]',
                    '.gradio-container > div:last-child',
                    '.gradio-container .wrap:last-child',
                    '.gradio-container .prose:last-child',
                    '.gradio-container .prose',
                    '.gradio-container div[class*="prose"]',
                    '.gradio-container div[class*="svelte"]',
                    'gradio-app > div:last-child',
                    '#root > div:last-child',
                    'body > div:last-child'
                ];
                
                selectors.forEach(selector => {
                    try {
                        document.querySelectorAll(selector).forEach(el => {
                            el.style.cssText = 'display: none !important; visibility: hidden !important; height: 0 !important; position: absolute !important; left: -99999px !important;';
                        });
                    } catch (e) {}
                });
                
                // 3. 隐藏所有链接
                document.querySelectorAll('a').forEach(link => {
                    if (link.href && (
                        link.href.includes('/docs') || 
                        link.href.includes('api') || 
                        link.href.includes('gradio') ||
                        link.href.includes('localhost') ||
                        link.href.includes('127.0.0.1')
                    )) {
                        link.style.cssText = 'display: none !important;';
                        if (link.parentElement) {
                            link.parentElement.style.cssText = 'display: none !important;';
                        }
                    }
                });
                
                // 4. 移除包含目标文本的元素
                document.querySelectorAll('div, span, p, a').forEach(el => {
                    if (el.textContent && el.textContent.trim().length < 50) {
                        const text = el.textContent.toLowerCase();
                        if (text.includes('api') || text.includes('gradio') || text.includes('设置')) {
                            el.remove();
                        }
                    }
                });
            }
            
            // 立即执行多次
            for (let i = 0; i < 10; i++) {
                setTimeout(nuclearHideFooter, i * 100);
            }
            
            // 定期执行
            setInterval(nuclearHideFooter, 1000);
            
            // DOM变化监听
            if (typeof MutationObserver !== 'undefined') {
                const observer = new MutationObserver(nuclearHideFooter);
                observer.observe(document.body, { 
                    childList: true, 
                    subtree: true,
                    attributes: true
                });
            }
            
            // 页面事件监听
            ['load', 'DOMContentLoaded', 'focus', 'resize'].forEach(event => {
                window.addEventListener(event, nuclearHideFooter);
            });
            
            console.log('🚀 核弹级底部信息栏隐藏脚本已激活');
        })();
        </script>
        """)'''
    
    # 在 return interface 之前添加终极JavaScript
    if '# ========== 终极JavaScript隐藏代码 ==========' not in content:
        content = content.replace(
            '\n\n    return interface',
            f'\n{ultimate_js}\n\n    return interface'
        )
        print("✅ 已添加终极JavaScript隐藏代码")
    
    # 写回文件
    with open('module2.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("🎉 终极隐藏方案已应用完成！")
    print("📝 应用的技术：")
    print("   ✅ Gradio show_api=False 参数")
    print("   ✅ 多层CSS强制隐藏")
    print("   ✅ 核弹级JavaScript动态隐藏")
    print("   ✅ DOM元素直接移除")
    print("   ✅ 实时监听和隐藏")

if __name__ == "__main__":
    apply_ultimate_hide_fix()
