#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复脚本：自动添加隐藏底部信息栏的参数
"""
import re

def fix_module2():
    """修复 module2.py 文件，添加隐藏底部信息栏的参数"""
    
    # 读取文件
    with open('module2.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复 gr.Blocks 参数
    blocks_pattern = r'(with gr\.Blocks\(\s*title="智能商机分析系统",\s*theme=gr\.themes\.Soft\(\),)'
    blocks_replacement = r'\1\n            show_api=False,'
    
    if 'show_api=False' not in content:
        content = re.sub(blocks_pattern, blocks_replacement, content)
        print("✅ 已在 gr.Blocks 中添加 show_api=False")
    
    # 修复第一个 interface.launch 参数
    launch1_pattern = r'(interface\.launch\(\s*server_name="0\.0\.0\.0",\s*server_port=7860,\s*share=True,\s*inbrowser=True,\s*prevent_thread_lock=False,\s*debug=False,\s*show_error=True,\s*quiet=False)\s*\)'
    launch1_replacement = r'\1,\n            show_api=False\n        )'
    
    content = re.sub(launch1_pattern, launch1_replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # 修复第二个 interface.launch 参数
    launch2_pattern = r'(interface\.launch\(\s*server_name="127\.0\.0\.1",\s*server_port=7860,\s*share=False,\s*inbrowser=True,\s*quiet=False)\s*\)'
    launch2_replacement = r'\1,\n                show_api=False\n            )'
    
    content = re.sub(launch2_pattern, launch2_replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # 添加强制隐藏的 CSS
    css_addition = '''
        
        /* 强制隐藏 Gradio 底部信息栏 */
        .footer, footer, 
        .gradio-container .footer, 
        .gradio-container footer,
        .app .footer, 
        .app footer,
        div[class*="footer"],
        div[class*="Footer"],
        .gradio-container > div:last-child,
        .gradio-container .wrap:last-child,
        .gradio-container .prose:last-child {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            overflow: hidden !important;
            opacity: 0 !important;
        }
        
        /* 隐藏包含特定文本的底部链接 */
        a[href*="api"], 
        a[href*="gradio"],
        div:has(a[href*="api"]),
        div:has(a[href*="gradio"]) {
            display: none !important;
        }'''
    
    # 在 CSS 的末尾添加隐藏规则
    if '/* 强制隐藏 Gradio 底部信息栏 */' not in content:
        content = content.replace(
            '        }\n        """\n    ) as interface:',
            f'        }}{css_addition}\n        """\n    ) as interface:'
        )
        print("✅ 已在 CSS 中添加隐藏规则")
    
    # 添加 JavaScript 隐藏代码
    js_code = '''
        # 强制隐藏底部信息栏
        gr.HTML("""
        <style>
        .footer, footer { display: none !important; visibility: hidden !important; }
        div[class*="footer"] { display: none !important; }
        div[class*="Footer"] { display: none !important; }
        .gradio-container > div:last-child { display: none !important; }
        .gradio-container .wrap:last-child { display: none !important; }
        .gradio-container .prose:last-child { display: none !important; }
        a[href*="api"] { display: none !important; }
        a[href*="gradio"] { display: none !important; }
        </style>
        <script>
        function hideGradioFooter() {
            const allElements = document.querySelectorAll('*');
            allElements.forEach(element => {
                if (element.textContent) {
                    const text = element.textContent.toLowerCase();
                    if (text.includes('通过 api 使用') || 
                        text.includes('使用 gradio 构建') || 
                        text.includes('设置') ||
                        text.includes('built with gradio') ||
                        (text.includes('api') && text.includes('gradio'))) {
                        element.style.display = 'none';
                        element.style.visibility = 'hidden';
                        element.style.height = '0';
                        element.style.overflow = 'hidden';
                        if (element.parentElement) {
                            element.parentElement.style.display = 'none';
                        }
                    }
                }
            });
            
            const footerSelectors = [
                '.footer', 'footer', 
                '.gradio-container .footer',
                '.gradio-container footer',
                'div[class*="footer"]',
                'div[class*="Footer"]'
            ];
            
            footerSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                    el.style.height = '0';
                });
            });
        }
        
        setTimeout(hideGradioFooter, 100);
        setTimeout(hideGradioFooter, 500);
        setTimeout(hideGradioFooter, 1000);
        setTimeout(hideGradioFooter, 2000);
        setInterval(hideGradioFooter, 3000);
        
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(hideGradioFooter);
            observer.observe(document.body, { 
                childList: true, 
                subtree: true 
            });
        }
        </script>
        """)
'''
    
    # 在 return interface 之前添加 JavaScript 代码
    if '# 强制隐藏底部信息栏' not in content:
        content = content.replace(
            '\n\n    return interface',
            f'\n{js_code}\n    return interface'
        )
        print("✅ 已添加 JavaScript 隐藏代码")
    
    # 写回文件
    with open('module2.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("🎉 修复完成！已添加所有隐藏底部信息栏的代码")

if __name__ == "__main__":
    fix_module2()
