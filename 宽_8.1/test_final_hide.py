#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：验证底部信息栏隐藏功能
"""
import gradio as gr

def test_function(text):
    return f"测试输出: {text}"

# 创建测试界面，应用所有隐藏技术
with gr.Blocks(
    title="底部信息栏隐藏测试",
    show_api=False,  # 关键参数1
    css="""
    /* 强力隐藏CSS */
    .footer, footer, 
    .gradio-container .footer, 
    .gradio-container footer,
    .app .footer, 
    .app footer,
    div[class*="footer"],
    div[class*="Footer"],
    .gradio-container > div:last-child,
    .gradio-container .wrap:last-child,
    .gradio-container .prose:last-child,
    .gradio-container > .wrap > div:last-child,
    body > gradio-app > div:last-child,
    gradio-app > div:last-child,
    #root > div:last-child,
    .gradio-container .svelte-1hnfib2,
    .gradio-container .prose.svelte-1ybaih5 {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
    }
    
    a[href*="api"], 
    a[href*="gradio"],
    a[href*="/docs"],
    div:has(a[href*="api"]),
    div:has(a[href*="gradio"]),
    div:has(a[href*="/docs"]) {
        display: none !important;
        visibility: hidden !important;
    }
    """
) as demo:
    
    gr.Markdown("# 🧪 底部信息栏隐藏测试")
    gr.Markdown("**如果成功，您应该看不到页面底部的任何 'API'、'Gradio' 或 '设置' 相关链接**")
    
    with gr.Row():
        input_text = gr.Textbox(
            label="输入测试文本",
            placeholder="输入任何文本进行测试"
        )
        
    with gr.Row():
        output_text = gr.Textbox(
            label="输出结果",
            interactive=False
        )
    
    # 绑定事件
    input_text.change(
        fn=test_function,
        inputs=input_text,
        outputs=output_text
    )
    
    # 强力JavaScript隐藏代码
    gr.HTML("""
    <style>
    .footer, footer { display: none !important; visibility: hidden !important; }
    div[class*="footer"] { display: none !important; }
    div[class*="Footer"] { display: none !important; }
    .gradio-container > div:last-child { display: none !important; }
    .gradio-container .wrap:last-child { display: none !important; }
    .gradio-container .prose:last-child { display: none !important; }
    a[href*="api"] { display: none !important; }
    a[href*="gradio"] { display: none !important; }
    .gradio-container .svelte-1hnfib2 { display: none !important; }
    .gradio-container .prose.svelte-1ybaih5 { display: none !important; }
    </style>
    <script>
    function hideFooterAggressively() {
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            if (element.textContent) {
                const text = element.textContent.toLowerCase().trim();
                if (text.includes('通过 api 使用') || 
                    text.includes('使用 gradio 构建') || 
                    text.includes('设置') ||
                    text.includes('built with gradio') ||
                    text === '通过 api 使用' ||
                    text === '使用 gradio 构建' ||
                    text === '设置') {
                    element.style.display = 'none';
                    element.style.visibility = 'hidden';
                    element.style.height = '0';
                    element.style.overflow = 'hidden';
                    element.style.opacity = '0';
                    element.style.position = 'absolute';
                    element.style.left = '-9999px';
                    if (element.parentElement) {
                        element.parentElement.style.display = 'none';
                    }
                }
            }
        });
        
        const footerSelectors = [
            '.footer', 'footer', 
            '.gradio-container .footer',
            '.gradio-container footer',
            'div[class*="footer"]',
            'div[class*="Footer"]',
            '.gradio-container > div:last-child',
            '.gradio-container .wrap:last-child',
            '.gradio-container .prose:last-child'
        ];
        
        footerSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                    el.style.height = '0';
                });
            } catch (e) {}
        });
        
        const links = document.querySelectorAll('a');
        links.forEach(link => {
            if (link.href && (link.href.includes('/docs') || 
                             link.href.includes('api') || 
                             link.href.includes('gradio'))) {
                link.style.display = 'none';
                if (link.parentElement) {
                    link.parentElement.style.display = 'none';
                }
            }
        });
    }
    
    setTimeout(hideFooterAggressively, 50);
    setTimeout(hideFooterAggressively, 100);
    setTimeout(hideFooterAggressively, 200);
    setTimeout(hideFooterAggressively, 500);
    setTimeout(hideFooterAggressively, 1000);
    setTimeout(hideFooterAggressively, 2000);
    setInterval(hideFooterAggressively, 2000);
    
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(hideFooterAggressively);
        observer.observe(document.body, { 
            childList: true, 
            subtree: true 
        });
    }
    
    window.addEventListener('load', function() {
        setTimeout(hideFooterAggressively, 100);
    });
    
    console.log('🔒 强力底部信息栏隐藏脚本已激活');
    </script>
    """)

if __name__ == "__main__":
    print("🧪 启动最终隐藏测试...")
    demo.launch(
        server_name="127.0.0.1",
        server_port=7863,
        share=False,
        show_api=False,  # 关键参数2
        inbrowser=True
    )
