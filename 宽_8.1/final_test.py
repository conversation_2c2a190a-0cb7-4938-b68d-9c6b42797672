#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：验证核弹级隐藏功能
"""
import gradio as gr

def test_func(text):
    return f"测试: {text}"

with gr.Blocks(
    title="核弹级隐藏测试",
    show_api=False,
    css="""
    .footer, footer, div[class*="footer"], div[class*="Footer"] { 
        display: none !important; 
        visibility: hidden !important; 
        height: 0 !important;
        position: absolute !important;
        left: -99999px !important;
    }
    .gradio-container > div:last-child,
    .gradio-container .wrap:last-child,
    .gradio-container .prose:last-child,
    .gradio-container .prose,
    .gradio-container div[class*="prose"],
    .gradio-container div[class*="svelte"] { 
        display: none !important; 
    }
    a[href*="api"], a[href*="gradio"], a[href*="/docs"] { 
        display: none !important; 
    }
    """
) as demo:
    
    # 核弹级隐藏
    gr.HTML("""
    <style>
    .footer, footer, div[class*="footer"], div[class*="Footer"] { 
        display: none !important; 
        visibility: hidden !important; 
        height: 0 !important;
        position: absolute !important;
        left: -99999px !important;
    }
    .gradio-container > div:last-child,
    .gradio-container .wrap:last-child,
    .gradio-container .prose:last-child,
    .gradio-container .prose,
    .gradio-container div[class*="prose"],
    .gradio-container div[class*="svelte"] { 
        display: none !important; 
    }
    a[href*="api"], a[href*="gradio"], a[href*="/docs"] { 
        display: none !important; 
    }
    </style>
    <script>
    (function() {
        function nuclearHide() {
            const targets = ['通过 api 使用', '使用 gradio 构建', '设置', 'built with gradio', 'api', 'gradio'];
            document.querySelectorAll('*').forEach(el => {
                if (el.textContent) {
                    const text = el.textContent.toLowerCase().trim();
                    for (const target of targets) {
                        if (text.includes(target) && text.length < 100) {
                            el.style.cssText = 'display: none !important; visibility: hidden !important;';
                            if (el.parentElement) el.parentElement.style.cssText = 'display: none !important;';
                            break;
                        }
                    }
                }
            });
            
            const selectors = ['.footer', 'footer', '.gradio-container > div:last-child', '.gradio-container .prose'];
            selectors.forEach(sel => {
                try {
                    document.querySelectorAll(sel).forEach(el => {
                        el.style.cssText = 'display: none !important;';
                    });
                } catch (e) {}
            });
            
            document.querySelectorAll('a').forEach(link => {
                if (link.href && (link.href.includes('api') || link.href.includes('gradio'))) {
                    link.style.cssText = 'display: none !important;';
                    if (link.parentElement) link.parentElement.style.cssText = 'display: none !important;';
                }
            });
        }
        
        for (let i = 0; i < 20; i++) setTimeout(nuclearHide, i * 100);
        setInterval(nuclearHide, 1000);
        if (typeof MutationObserver !== 'undefined') {
            new MutationObserver(nuclearHide).observe(document.body, { childList: true, subtree: true });
        }
        ['load', 'DOMContentLoaded', 'focus'].forEach(e => window.addEventListener(e, nuclearHide));
        console.log('🚀 核弹级隐藏已激活');
    })();
    </script>
    """)
    
    gr.Markdown("# 🚀 核弹级隐藏测试")
    gr.Markdown("**如果成功，底部应该完全没有任何API、Gradio或设置相关的内容**")
    
    with gr.Row():
        input_box = gr.Textbox(label="测试输入")
        output_box = gr.Textbox(label="测试输出")
    
    input_box.change(fn=test_func, inputs=input_box, outputs=output_box)

if __name__ == "__main__":
    print("🚀 启动核弹级隐藏测试...")
    demo.launch(
        server_name="127.0.0.1",
        server_port=7864,
        share=False,
        show_api=False,
        inbrowser=True
    )
