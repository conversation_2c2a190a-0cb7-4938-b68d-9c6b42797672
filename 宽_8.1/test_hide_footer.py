#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试隐藏 Gradio 底部信息栏的功能
"""
import gradio as gr

def create_test_interface():
    """创建测试界面"""
    with gr.Blocks(
        title="测试隐藏底部信息栏",
        theme=gr.themes.Soft(),
        show_api=False,  # 隐藏API信息
        css="""
        /* 强制隐藏所有可能的底部信息栏 */
        .footer, footer { 
            display: none !important; 
            visibility: hidden !important; 
            height: 0 !important;
            overflow: hidden !important;
        }
        div[class*="footer"], div[class*="Footer"] { 
            display: none !important; 
        }
        .gradio-container > div:last-child { 
            display: none !important; 
        }
        .gradio-container .wrap:last-child { 
            display: none !important; 
        }
        .gradio-container .prose:last-child { 
            display: none !important; 
        }
        a[href*="api"], a[href*="gradio"] { 
            display: none !important; 
        }
        /* 隐藏包含特定文本的元素 */
        div:has(a[href*="api"]), div:has(a[href*="gradio"]) {
            display: none !important;
        }
        """
    ) as interface:
        
        gr.HTML("""
        <h1>🧪 测试隐藏底部信息栏</h1>
        <p>这个界面用于测试是否成功隐藏了 Gradio 底部的信息栏</p>
        """)
        
        with gr.Row():
            input_text = gr.Textbox(
                label="输入测试文本",
                placeholder="请输入一些文本进行测试"
            )
            
        with gr.Row():
            output_text = gr.Textbox(
                label="输出结果",
                interactive=False
            )
            
        def process_text(text):
            return f"您输入的文本是: {text}"
            
        input_text.change(
            fn=process_text,
            inputs=[input_text],
            outputs=[output_text]
        )
        
        # 强制隐藏底部信息栏的 JavaScript
        gr.HTML("""
        <style>
        /* 额外的CSS规则 */
        .footer, footer { display: none !important; }
        div[class*="footer"] { display: none !important; }
        </style>
        <script>
        function hideGradioFooter() {
            // 隐藏包含特定文本的所有元素
            const allElements = document.querySelectorAll('*');
            allElements.forEach(element => {
                if (element.textContent) {
                    const text = element.textContent.toLowerCase();
                    if (text.includes('通过 api 使用') || 
                        text.includes('使用 gradio 构建') || 
                        text.includes('设置') ||
                        text.includes('built with gradio') ||
                        (text.includes('api') && text.includes('gradio'))) {
                        element.style.display = 'none';
                        element.style.visibility = 'hidden';
                        element.style.height = '0';
                        element.style.overflow = 'hidden';
                        // 也隐藏父元素
                        if (element.parentElement) {
                            element.parentElement.style.display = 'none';
                        }
                    }
                }
            });
            
            // 隐藏所有可能的底部容器
            const footerSelectors = [
                '.footer', 'footer', 
                '.gradio-container .footer',
                '.gradio-container footer',
                'div[class*="footer"]',
                'div[class*="Footer"]'
            ];
            
            footerSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                    el.style.height = '0';
                });
            });
        }
        
        // 页面加载后立即执行
        setTimeout(hideGradioFooter, 100);
        setTimeout(hideGradioFooter, 500);
        setTimeout(hideGradioFooter, 1000);
        setTimeout(hideGradioFooter, 2000);
        
        // 定期检查并隐藏
        setInterval(hideGradioFooter, 3000);
        
        // 监听DOM变化
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(hideGradioFooter);
            observer.observe(document.body, { 
                childList: true, 
                subtree: true 
            });
        }
        </script>
        """)
    
    return interface

if __name__ == "__main__":
    print("🧪 启动测试界面...")
    interface = create_test_interface()
    interface.launch(
        server_name="127.0.0.1",
        server_port=7861,
        share=False,
        inbrowser=True,
        show_api=False
    )
