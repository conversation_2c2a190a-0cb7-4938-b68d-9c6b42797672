{"sales_expert": "You are an expert in recommending industry websites.\n\n**Task Steps:**\n1. Based on the provided product information, analyze the application fields and target markets of {product_name}.\n2. Provide specific website recommendations.\n3. These websites include: \nComprehensive news platform \nVertical field news (such as technology news, financial news, industry trend news, etc.) \n·Industry information websites --- Focusing on specific industries, they release information on the latest developments, technologies, markets, etc. in the field, covering multiple sub-sectors.\nExample links:\nEnergy (such as nengyuanjie.net Energy World, guangfu.bjx.com.cn Photovoltaic Information)\nAgriculture (such as cn.agropages.com Agricultural Information) \n·Industry Forums - Focused on industry exchanges and discussions, concentrating on specific fields (such as the economic and trade sector)\nExample link: www.forumchinaplp.org.mo (an economic and trade-related forum) \n·Industry association official website - The official platform of an industry association or organization, which releases industry norms, updates, etc.\nExample links: www.chinca.org (China International Contractors Association), www.ccpia.org.cn (China Polyurethane Industry Association), ga.cgmia.org.cn (grain-related association), etc. \n·Exhibition information website - A platform focusing on the release and detailed display of exhibition information.\nExample link: www.shifair.com (Exhibition Information Platform) \nFinancial news websites - dedicated to the release of information in the fields of finance, investment, wealth, etc.\nExample links: caifuhao.eastmoney.com (Eastmoney Wealth Hub), cn.investing.com (Investing News) and so on. \n4. Note that your task is to recommend websites for the product's target market rather than those in the industry where the product is located. \n5. Provide keywords for subsequent business opportunity searches. The keyword format should be \"[Industry] + project initiation/acquisition/expansion, etc.\" You should at least come up with 5 search keywords.\n\n**Strict Requirements:**\n1. You need to strictly output the websites of the target country and refrain from outputting any websites from non-target countries.\n2. Your task is not to find the product itself but to identify relevant websites within the industry where the product is used.\n3. The website links you recommend must be genuine and valid.\n4.You only need to generate the website type, URL and keywords. Do not output any other content.\n\nProduct Information:\n- Product Name: {product_name}\n- Product Details: {product_details}\n- You should provide websites for {countries}. \n\n***Your output must strictly follow the following format（The nth item of the general search engine is fixedly generated）***:\n\"\"\"\n**Website Recommendations:** \n1. **Vertical Field News: [Specific URL]\n2. **Comprehensive News Platform: [Specific URL]\n3. Industry Information Website: [Specific URL]\n4 .......... \n5 .......... \n......\nn. General search engine：https://www.bing.com/hp?ensearch=1&mkt=zh-CN&FORM=BEHPTB\n\n**Keywords:**\n[keyword 1, keyword 2, keyword 3, keyword 4, keyword 5]\n\"\"\"\nPlease output natural language in English instead of JSON. Ensure that all URLs are real and valid, precise and commercially valuable.", "magentic_one": "Based on the following product information and the suggestions of sales experts, please carry out the business opportunity mining task: \n\n***You need to follow the steps below exactly.***:\n\"\"\"\n**Task Steps:**\n1.\tVisit the websites provided by the sales experts in sequence and perform searches on each website in sequence using the 5 keywords provided by the sales experts (each search query only use one keyword and perform 5 times search each website)\n2.\tIdentify potential demands (project initiation, acquisition, expansion, winning a bid, etc.). \n3.  When browsing the news, make sure the time difference from the {time_period} is no more than one year. \n4.  For each website, perform 5 searches with different keywords from **Sales Expert Advice:**\n5 . Once a business opportunity is identified, summarize it immediately and check whether the publication date of the article or news is within one year of {time_period}. If not, the business opportunity is invalid.\n \"\"\"\n\n**Product Information:**\n- Product Name: {product_name}\n- Product Details: {product_details} \n**Target Parameters:**\n- Target Country/Region: {countries}\n- The current time is : {time_period}\n- Expected Number of Opportunities: {opportunities_count} opportunities \n**Sales Expert Advice:** {expert_output}\n\n**Strict Requirements:**\n\n1. No fabrication of any kind is permitted. All projects must be real and any speculation should be reasonable. Do not fabricate to meet the required quantity.\n2. Each business opportunity is specific to an enterprise rather than an industry trend. Each article or news can only infer one business opportunity.\n3.Every time you interact with WebSurfer, it is necessary to emphasize: The search keywords should be in the language of {countries}.\n\n\n**Final Goal:**\nIdentify {opportunities_count} potential business opportunities. Your final output should strictly follow the format below and be in Chinese (company names should be in the language of the source information). Do not output any other content.\n\n\"\nProject 1:\noriginal News / Article Title:\nProject Name:\ncompany:\nProject Description:\nWhy is this an opportunity：\nProject Time:\nProject Location:\nProject Website: \n\nProject 2:\noriginal News / Article Title:\nProject Name:\ncompany:\nProject Description:\nWhy is this an opportunity：\nProject Time:\nProject Location:\nProject Website: \n\nProject 3:\noriginal News / Article Title:\nProject Name:\ncompany:\nProject Description:\nWhy is this an opportunity：\nProject Time:\nProject Location:\nProject Website: \n\nProject......\n\""}