#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试隐藏 Gradio 底部信息栏
"""
import gradio as gr

def simple_function(text):
    return f"您输入的是: {text}"

# 创建简单界面
with gr.Blocks(
    title="隐藏底部信息栏测试",
    show_api=False,  # 这是关键参数
    css="""
    /* 隐藏底部信息栏的CSS */
    .footer, footer { 
        display: none !important; 
    }
    div[class*="footer"] { 
        display: none !important; 
    }
    """
) as demo:
    gr.Markdown("# 测试隐藏底部信息栏")
    gr.Markdown("如果成功，您应该看不到底部的 '通过 API 使用' 等信息")
    
    with gr.Row():
        input_box = gr.Textbox(label="输入测试")
        output_box = gr.Textbox(label="输出结果")
    
    input_box.change(fn=simple_function, inputs=input_box, outputs=output_box)

if __name__ == "__main__":
    demo.launch(
        server_name="127.0.0.1",
        server_port=7862,
        share=False,
        show_api=False  # 这也是关键参数
    )
